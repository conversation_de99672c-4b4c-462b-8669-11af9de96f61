import {
  DataTestSelectorRightPanelVideo,
  DataTestSelector,
} from '../../../support/helperFunction/mediaDetailHelper';
import { mediaDetailPage } from '../mediaDetail.po';

export const createMergedGroup = (): void => {
  cy.get(
    `[data-testid=${DataTestSelectorRightPanelVideo.ListSortContainer}] [data-testid=${DataTestSelectorRightPanelVideo.CheckBoxContainer}]`
  ).each(($container: JQuery<HTMLElement>) => {
    if ($container.find('svg').length > 0) {
      cy.wrap($container).click();
    }
  });

  cy.getDataIdCy({ idAlias: DataTestSelector.ClusterRow })
    .filter(':contains("PERSON")')
    .eq(0)
    .within(() => {
      mediaDetailPage.nameGroup().click();
      mediaDetailPage.nameGroupInput().find('input').clear();
      mediaDetailPage.nameGroupInput().find('input').type('Group 1');
      mediaDetailPage.acceptIcon().click();
      mediaDetailPage.nameGroup().find('p').contains('Group 1');
    });

  cy.getDataIdCy({ idAlias: DataTestSelector.ClusterRow })
    .filter(':contains("HEAD")')
    .eq(0)
    .within(() => {
      mediaDetailPage.nameGroup().click();
      mediaDetailPage.nameGroupInput().find('input').clear();
      mediaDetailPage.nameGroupInput().find('input').type('Group 2');
      mediaDetailPage.acceptIcon().click();
      mediaDetailPage.nameGroup().find('p').contains('Group 2');
    });

  mediaDetailPage.groupItemMenu().eq(1).click({ force: true });
  mediaDetailPage.mergedWidthNamedGroup().click();
  cy.get('[role="button"]').contains('Group 1').click();
};

export const warningModalView = (
  header: string,
  body1: string,
  body2: string
): void => {
  mediaDetailPage.confirmDialogTitle().contains(header).should('be.visible');
  mediaDetailPage.confirmDialogContent().contains(body1).should('be.visible');
  mediaDetailPage.confirmDialogContent().contains(body2).should('be.visible');
};

export const verifyButtonInMergeGroup = (color: string): void => {
  mediaDetailPage
    .confirmDialogButton()
    .contains('Cancel')
    .toHaveCssProperty(
      'background',
      'rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box'
    );
  mediaDetailPage
    .confirmDialogButton()
    .contains('Group')
    .toHaveCssProperty('background-color', color);
  mediaDetailPage
    .confirmDialogButton()
    .contains('Merged Group')
    .toHaveCssProperty('background-color', color);
};

export const warningModalRadioButtonView = (
  header: string,
  body1: string,
  body2: string,
  body3: string
): void => {
  mediaDetailPage.confirmDialogTitle().contains(header).should('be.visible');
  mediaDetailPage.confirmDialogContent().find('p').should('contain', body1);
  mediaDetailPage.confirmDialogContent().contains(body3);

  cy.get('[id="demo-row-radio-buttons-group-label"]')
    .contains(body2)
    .should('be.visible');
  cy.get('[name="row-radio-buttons-group"]')
    .eq(0)
    .should('have.value', 'ellipse');
  cy.get('[name="row-radio-buttons-group"]')
    .eq(1)
    .should('have.value', 'rectangle');
  cy.get('[name="row-radio-buttons-group"]')
    .eq(2)
    .should('have.value', 'default');

  mediaDetailPage.confirmDialogButton().contains('Cancel').should('be.visible');
  mediaDetailPage.confirmDialogButton().contains('Group').should('be.visible');
  mediaDetailPage
    .confirmDialogButton()
    .contains('Merged Group')
    .should('be.visible');
};

export const verifyButtonInRedContainer = (): void => {
  mediaDetailPage.confirmDialogButton().contains('Cancel').should('be.visible');
  mediaDetailPage.confirmDialogButton().contains('Group').should('be.visible');
  mediaDetailPage
    .confirmDialogButton()
    .contains('Merged Group')
    .should('be.visible');
};

export const created2ndMergedGroup = (): void => {
  cy.get(
    `[data-testid=${DataTestSelectorRightPanelVideo.ListSortContainer}] [data-testid=${DataTestSelectorRightPanelVideo.CheckBoxContainer}]`
  ).each(($container: JQuery<HTMLElement>) => {
    if ($container.find('svg').length > 0) {
      cy.wrap($container).click();
    }
  });

  cy.getDataIdCy({ idAlias: DataTestSelector.ClusterRow }).then(
    ($rows: JQuery<HTMLElement>) => {
      for (let i = 0; i < $rows.length; i++) {
        const rowElement = $rows.get(i);
        const row2ndElement = $rows.get(i + 1);

        if (!rowElement) {
          throw new Error(`Row element at index ${i} not found`);
        }

        const $row = Cypress.$(rowElement);
        const detectionName = $row
          .find(`[data-test=${DataTestSelectorRightPanelVideo.DetectionName}]`)
          .text()
          .trim();

        let detectionName2nd = '';
        if (row2ndElement) {
          const $row2nd = Cypress.$(row2ndElement);
          detectionName2nd = $row2nd
            .find(`[data-test=${DataTestSelectorRightPanelVideo.DetectionName}]`)
            .text()
            .trim();
        }

      if (detectionName === 'HEAD') {
        cy.wrap($row)
          .eq(0)
          .within(() => {
            mediaDetailPage.nameGroup().click();

            mediaDetailPage.nameGroupInput().find('input').clear();

            mediaDetailPage.nameGroupInput().find('input').type('Group 1');

            mediaDetailPage.acceptIcon().click();

            mediaDetailPage.nameGroup().find('p').contains('Group 1');
          });
      }

      if (detectionName2nd === 'HEAD' && row2ndElement) {
        cy.wrap(Cypress.$(row2ndElement as HTMLElement))
          .eq(0)
          .within(() => {
            mediaDetailPage.nameGroup().click();

            mediaDetailPage.nameGroupInput().find('input').clear();

            mediaDetailPage.nameGroupInput().find('input').type('Group 2');

            mediaDetailPage.acceptIcon().click();

            mediaDetailPage.nameGroup().find('p').contains('Group 2');
          });
        break;
      }
    }
    return undefined;
  });

  mediaDetailPage.groupItemMenu().eq(1).click({ force: true });
  mediaDetailPage.mergedWidthNamedGroup().click();
  cy.get('[role="button"]').contains('Group 1').click();
};
